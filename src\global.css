@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
	body {
		@apply bg-background text-text;
	}
}

@layer components {
	.btn-primary {
		@apply w-full border border-transparent rounded-md py-2 px-4 flex items-center justify-center text-base font-medium text-white bg-primary hover:bg-secondary focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-offset-gray-800 focus:ring-primary transition-colors duration-200;
	}

	.input-text {
		@apply appearance-none min-w-0 w-full bg-gray-700 border border-gray-600 rounded-md py-2 px-4 text-base text-text placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-offset-gray-800 focus:ring-white focus:border-white focus:placeholder-gray-300;
	}
}

@layer utilities {
	/* Custom animations for floating icons */
	@keyframes float {
		0%,
		100% {
			transform: translateY(0px) translateX(0px);
		}
		25% {
			transform: translateY(-10px) translateX(5px);
		}
		50% {
			transform: translateY(-20px) translateX(-5px);
		}
		75% {
			transform: translateY(-10px) translateX(3px);
		}
	}

	@keyframes drift {
		0% {
			transform: translate(0, 0) rotate(0deg);
		}
		25% {
			transform: translate(var(--drift-x, 20px), var(--drift-y, -15px)) rotate(90deg);
		}
		50% {
			transform: translate(calc(var(--drift-x, 20px) * -0.5), calc(var(--drift-y, -15px) * 1.5))
				rotate(180deg);
		}
		75% {
			transform: translate(calc(var(--drift-x, 20px) * -1), var(--drift-y, -15px)) rotate(270deg);
		}
		100% {
			transform: translate(0, 0) rotate(360deg);
		}
	}

	@keyframes pulse-slow {
		0%,
		100% {
			opacity: 0.4;
			transform: scale(1);
		}
		50% {
			opacity: 1;
			transform: scale(1.1);
		}
	}

	@keyframes bounce-slow {
		0%,
		100% {
			transform: translateY(0);
			animation-timing-function: cubic-bezier(0.8, 0, 1, 1);
		}
		50% {
			transform: translateY(-25%);
			animation-timing-function: cubic-bezier(0, 0, 0.2, 1);
		}
	}

	@keyframes spin-slow {
		from {
			transform: rotate(0deg);
		}
		to {
			transform: rotate(360deg);
		}
	}

	.animate-float {
		animation: float 4s ease-in-out infinite;
	}

	.animate-drift {
		animation: drift 6s ease-in-out infinite;
	}

	.animate-pulse-slow {
		animation: pulse-slow 3s ease-in-out infinite;
	}

	.animate-bounce-slow {
		animation: bounce-slow 4s ease-in-out infinite;
	}

	.animate-spin-slow {
		animation: spin-slow 8s linear infinite;
	}

	@keyframes orbit {
		0% {
			transform: rotate(0deg) translateX(var(--orbit-radius, 30px)) rotate(0deg);
		}
		100% {
			transform: rotate(360deg) translateX(var(--orbit-radius, 30px)) rotate(-360deg);
		}
	}

	.animate-orbit {
		animation: orbit 6s linear infinite;
	}

	/* Enhanced floating animations with more complex paths */
	@keyframes float-complex {
		0% {
			transform: translateY(0px) translateX(0px) rotate(0deg);
		}
		20% {
			transform: translateY(-15px) translateX(10px) rotate(72deg);
		}
		40% {
			transform: translateY(-25px) translateX(-5px) rotate(144deg);
		}
		60% {
			transform: translateY(-10px) translateX(15px) rotate(216deg);
		}
		80% {
			transform: translateY(-20px) translateX(-10px) rotate(288deg);
		}
		100% {
			transform: translateY(0px) translateX(0px) rotate(360deg);
		}
	}

	.animate-float-complex {
		animation: float-complex 8s ease-in-out infinite;
	}

	/* Sparkle effect for special icons */
	@keyframes sparkle {
		0%,
		100% {
			opacity: 0.4;
			transform: scale(1) rotate(0deg);
			filter: brightness(1);
		}
		25% {
			opacity: 0.8;
			transform: scale(1.1) rotate(90deg);
			filter: brightness(1.3);
		}
		50% {
			opacity: 1;
			transform: scale(1.2) rotate(180deg);
			filter: brightness(1.5);
		}
		75% {
			opacity: 0.8;
			transform: scale(1.1) rotate(270deg);
			filter: brightness(1.3);
		}
	}

	.animate-sparkle {
		animation: sparkle 4s ease-in-out infinite;
	}

	/* Gentle sway animation */
	@keyframes sway {
		0%,
		100% {
			transform: rotate(-3deg) translateX(0px);
		}
		50% {
			transform: rotate(3deg) translateX(5px);
		}
	}

	.animate-sway {
		animation: sway 3s ease-in-out infinite;
	}

	/* Responsive animation adjustments */
	@media (max-width: 768px) {
		.animate-float,
		.animate-float-complex,
		.animate-drift,
		.animate-orbit {
			animation-duration: 1.5s !important;
		}

		.animate-pulse-slow,
		.animate-sparkle {
			animation-duration: 2s !important;
		}

		.animate-bounce-slow {
			animation-duration: 2.5s !important;
		}

		.animate-spin-slow {
			animation-duration: 4s !important;
		}
	}

	/* Reduce motion for users who prefer it */
	@media (prefers-reduced-motion: reduce) {
		.animate-float,
		.animate-float-complex,
		.animate-drift,
		.animate-orbit,
		.animate-bounce-slow,
		.animate-spin-slow,
		.animate-sparkle,
		.animate-sway {
			animation: none !important;
		}

		.animate-pulse-slow {
			animation: pulse-slow 3s ease-in-out infinite !important;
		}
	}
}
