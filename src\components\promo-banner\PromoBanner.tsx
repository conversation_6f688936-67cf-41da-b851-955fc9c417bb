import { component$, useSignal, useVisibleTask$ } from '@qwik.dev/core';
import { getCurrentPromotion } from '~/providers/shop/promotions/promotions';

export default component$(() => {
	const promotion = useSignal<{
		title: string;
		code: string;
		description: string;
	} | null>(null);

	useVisibleTask$(() => {
		// Get current promotion on component mount
		promotion.value = getCurrentPromotion();
	});

	if (!promotion.value) {
		return null;
	}

	return (
		<div class="hidden sm:block">
			<p class="text-sm">
				{promotion.value.title}{' '}
				<span class="font-bold transition-colors text-cyan-500 hover:text-cyan-400">
					Promo Code: {promotion.value.code}
				</span>
			</p>
		</div>
	);
});
