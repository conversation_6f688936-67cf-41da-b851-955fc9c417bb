import { component$, useSignal, useVisibleTask$ } from '@qwik.dev/core';

interface AnimatedIconProps {
	icon: any;
	animationType:
		| 'float'
		| 'rotate'
		| 'pulse'
		| 'bounce'
		| 'drift'
		| 'orbit'
		| 'sparkle'
		| 'sway'
		| 'float-complex';
	size?: 'sm' | 'md' | 'lg';
	color?: string;
	delay?: number;
	duration?: number;
	initialX?: number;
	initialY?: number;
}

export default component$<AnimatedIconProps>(
	({
		icon: IconComponent,
		animationType,
		size = 'md',
		color = 'text-primary',
		delay = 0,
		duration = 3,
		initialX = 0,
		initialY = 0,
	}) => {
		const elementRef = useSignal<HTMLDivElement>();

		const sizeClasses = {
			sm: 'w-6 h-6',
			md: 'w-8 h-8',
			lg: 'w-12 h-12',
		};

		const getAnimationClass = () => {
			switch (animationType) {
				case 'float':
					return 'animate-float';
				case 'rotate':
					return 'animate-spin-slow';
				case 'pulse':
					return 'animate-pulse-slow';
				case 'bounce':
					return 'animate-bounce-slow';
				case 'drift':
					return 'animate-drift';
				case 'orbit':
					return 'animate-orbit';
				case 'sparkle':
					return 'animate-sparkle';
				case 'sway':
					return 'animate-sway';
				case 'float-complex':
					return 'animate-float-complex';
				default:
					return 'animate-float';
			}
		};

		useVisibleTask$(() => {
			if (elementRef.value) {
				// Set initial position
				elementRef.value.style.left = `${initialX}%`;
				elementRef.value.style.top = `${initialY}%`;

				// Set animation delay and duration
				elementRef.value.style.animationDelay = `${delay}s`;
				elementRef.value.style.animationDuration = `${duration}s`;

				// Add random drift for drift animation
				if (animationType === 'drift') {
					const randomX = Math.random() * 50 + 10; // 10-60px
					const randomY = Math.random() * 50 + 10; // 10-60px
					elementRef.value.style.setProperty('--drift-x', `${randomX}px`);
					elementRef.value.style.setProperty('--drift-y', `${randomY}px`);
				}

				// Add random orbit radius for orbit animation
				if (animationType === 'orbit') {
					const radius = Math.random() * 30 + 20; // 20-50px
					elementRef.value.style.setProperty('--orbit-radius', `${radius}px`);
				}

				// Add entrance animation
				elementRef.value.style.opacity = '0';
				elementRef.value.style.transform = 'scale(0)';

				setTimeout(() => {
					if (elementRef.value) {
						elementRef.value.style.transition = 'opacity 0.5s ease-out, transform 0.5s ease-out';
						elementRef.value.style.opacity = '0.6';
						elementRef.value.style.transform = 'scale(1)';
					}
				}, delay * 1000);
			}
		});

		return (
			<div
				ref={elementRef}
				class={`absolute ${sizeClasses[size]} ${color} ${getAnimationClass()} pointer-events-none hover:opacity-100 transition-opacity duration-300 will-change-transform`}
				style={{
					animationIterationCount: 'infinite',
					animationTimingFunction: 'ease-in-out',
					backfaceVisibility: 'hidden',
					perspective: '1000px',
				}}
			>
				<IconComponent />
			</div>
		);
	}
);
