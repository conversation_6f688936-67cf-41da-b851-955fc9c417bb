import { component$ } from '@qwik.dev/core';
import AnimatedIcon from './AnimatedIcon';

// Import available icons
import CheckCircleIcon from '../icons/CheckCircleIcon';
import CreditCardIcon from '../icons/CreditCardIcon';
import GitIcon from '../icons/GitIcon';
import HeartIcon from '../icons/HeartIcon';
import PencilIcon from '../icons/PencilIcon';
import ShieldCheckIcon from '../icons/ShieldCheckIcon';
import ShoppingBagIcon from '../icons/ShoppingBagIcon';
import StarIcon from '../icons/StarIcon';

export default component$(() => {
	// Define floating icons with their properties
	const floatingIcons = [
		{
			icon: HeartIcon,
			animationType: 'float' as const,
			size: 'md' as const,
			color: 'text-primary',
			delay: 0,
			duration: 4,
			initialX: 10,
			initialY: 20,
		},
		{
			icon: StarIcon,
			animationType: 'pulse' as const,
			size: 'sm' as const,
			color: 'text-accent1',
			delay: 1,
			duration: 3,
			initialX: 85,
			initialY: 15,
		},
		{
			icon: ShoppingBagIcon,
			animationType: 'drift' as const,
			size: 'lg' as const,
			color: 'text-accent2',
			delay: 0.5,
			duration: 6,
			initialX: 75,
			initialY: 60,
		},
		{
			icon: CreditCardIcon,
			animationType: 'rotate' as const,
			size: 'md' as const,
			color: 'text-secondary',
			delay: 2,
			duration: 8,
			initialX: 15,
			initialY: 70,
		},
		{
			icon: ShieldCheckIcon,
			animationType: 'bounce' as const,
			size: 'sm' as const,
			color: 'text-primary',
			delay: 1.5,
			duration: 4,
			initialX: 90,
			initialY: 45,
		},
		{
			icon: PencilIcon,
			animationType: 'sparkle' as const,
			size: 'md' as const,
			color: 'text-accent1',
			delay: 3,
			duration: 5,
			initialX: 5,
			initialY: 45,
		},
		{
			icon: CheckCircleIcon,
			animationType: 'pulse' as const,
			size: 'lg' as const,
			color: 'text-accent2',
			delay: 0.8,
			duration: 3.5,
			initialX: 80,
			initialY: 25,
		},
		{
			icon: GitIcon,
			animationType: 'drift' as const,
			size: 'sm' as const,
			color: 'text-secondary',
			delay: 2.5,
			duration: 7,
			initialX: 25,
			initialY: 80,
		},
		{
			icon: StarIcon,
			animationType: 'float-complex' as const,
			size: 'sm' as const,
			color: 'text-primary',
			delay: 4,
			duration: 4.5,
			initialX: 60,
			initialY: 10,
		},
		{
			icon: HeartIcon,
			animationType: 'pulse' as const,
			size: 'md' as const,
			color: 'text-accent1',
			delay: 1.2,
			duration: 3.8,
			initialX: 95,
			initialY: 75,
		},
		{
			icon: StarIcon,
			animationType: 'sway' as const,
			size: 'sm' as const,
			color: 'text-secondary',
			delay: 5,
			duration: 3.2,
			initialX: 40,
			initialY: 85,
		},
		{
			icon: ShieldCheckIcon,
			animationType: 'rotate' as const,
			size: 'md' as const,
			color: 'text-accent2',
			delay: 3.5,
			duration: 6,
			initialX: 70,
			initialY: 5,
		},
	];

	return (
		<div class="absolute inset-0 overflow-hidden pointer-events-none will-change-transform">
			{floatingIcons.map((iconProps, index) => (
				<AnimatedIcon
					key={index}
					icon={iconProps.icon}
					animationType={iconProps.animationType}
					size={iconProps.size}
					color={iconProps.color}
					delay={iconProps.delay}
					duration={iconProps.duration}
					initialX={iconProps.initialX}
					initialY={iconProps.initialY}
				/>
			))}
		</div>
	);
});
