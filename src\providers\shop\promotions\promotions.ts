import gql from 'graphql-tag';
import { Promotion } from '~/generated/graphql';
import { shopSdk } from '~/graphql-wrapper';

export const getActivePromotionsQuery = async (): Promise<Promotion[]> => {
	try {
		// Note: Vendure shop API typically doesn't expose promotions directly to customers
		// This is a placeholder for when/if the API supports it
		// For now, we'll return an empty array and handle promotions through other means
		return [];
	} catch (error) {
		console.warn('Failed to fetch active promotions:', error);
		return [];
	}
};

// Alternative approach: Get promotion info from active order discounts
export const getActiveOrderPromotions = async () => {
	try {
		const activeOrder = await shopSdk.activeOrder();
		if (activeOrder?.activeOrder?.discounts) {
			return activeOrder.activeOrder.discounts.map(discount => ({
				description: discount.description,
				amount: discount.amountWithTax,
				type: discount.type
			}));
		}
		return [];
	} catch (error) {
		console.warn('Failed to fetch active order promotions:', error);
		return [];
	}
};

// For now, we'll use a simple function to get current promotions
// This can be replaced with actual API calls when available
export const getCurrentPromotion = () => {
	const currentDate = new Date();
	const currentMonth = currentDate.getMonth();
	
	// Example: Different promotions based on time of year
	if (currentMonth >= 10 || currentMonth <= 1) { // Nov-Jan (Holiday season)
		return {
			title: "Holiday Special: 15% OFF",
			code: "HOLIDAY15",
			description: "Holiday savings on all items"
		};
	} else if (currentMonth >= 5 && currentMonth <= 7) { // Jun-Aug (Summer)
		return {
			title: "Summer Sale: 10% OFF",
			code: "SUMMER10",
			description: "Beat the heat with summer savings"
		};
	} else {
		return {
			title: "New Customer Special: 10% OFF",
			code: "WELCOME10",
			description: "Welcome discount for new customers"
		};
	}
};
