import { $, component$, useSignal } from '@qwik.dev/core';
import XCircleIcon from '~/components/icons/XCircleIcon';
import { requestPasswordResetMutation } from '~/providers/shop/account/account';

export default component$(() => {
	const email = useSignal('');
	const error = useSignal('');
	const success = useSignal(false);

	const reset = $(async () => {
		const requestPasswordReset = await requestPasswordResetMutation(email.value);
		requestPasswordReset?.__typename === 'Success'
			? (success.value = true)
			: (error.value = requestPasswordReset?.message ?? 'Reset password error');
	});
	return (
		<div class="min-h-screen flex flex-col justify-center py-12 sm:px-6 lg:px-8 bg-gray-50">
			<div class="sm:mx-auto sm:w-full sm:max-w-md">
				<h2 class="mt-6 text-center text-3xl font-bold text-gray-900">Reset password</h2>
				<p class="mt-2 text-center text-sm text-gray-600">
					Enter your email and we'll send you a link to reset your password
				</p>
			</div>
			<div class="mt-8 sm:mx-auto sm:w-full sm:max-w-md">
				<div class="bg-white py-8 px-4 shadow sm:rounded-lg sm:px-10">
					{success.value && (
						<div class="mb-6 bg-green-50 border border-green-200 text-green-800 rounded p-4 text-center text-sm">
							<p>Password reset email sent to {email.value}</p>
						</div>
					)}
					<div class="space-y-6">
						<div>
							<label class="block text-sm font-medium text-gray-700">Email</label>
							<div class="mt-1">
								<input
									type="email"
									value={email.value}
									required
									onInput$={(_, el) => (email.value = el.value)}
									onKeyUp$={(ev, el) => {
										error.value = '';
										if (ev.key === 'Enter' && !!el.value) {
											reset();
										}
									}}
									class="appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
								/>
							</div>
						</div>

						{error.value !== '' && (
							<div class="rounded-md bg-red-50 p-4">
								<div class="flex">
									<div class="flex-shrink-0">
										<XCircleIcon />
									</div>
									<div class="ml-3">
										<h3 class="text-sm font-medium text-red-800">
											We ran into a problem resetting your password!
										</h3>
										<p class="text-sm text-red-700 mt-2">{error.value}</p>
									</div>
								</div>
							</div>
						)}
						<div>
							<button
								class="w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-colors duration-200"
								onClick$={reset}
							>
								Reset password
							</button>
						</div>
					</div>
				</div>
			</div>
		</div>
	);
});
