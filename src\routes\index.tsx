import { component$, useContext } from '@qwik.dev/core';
import { Link } from '@qwik.dev/router';
import { Image } from 'qwik-image';
import FloatingIcons from '~/components/animations/FloatingIcons';
import CollectionCard from '~/components/collection-card/CollectionCard';
import { APP_STATE, HOMEPAGE_IMAGE } from '~/constants';
import { $localize } from '~/utils/localize';

export default component$(() => {
	const collections = useContext(APP_STATE).collections;

	return (
		<div>
			<div class="relative h-[600px] overflow-hidden">
				<div class="absolute inset-0 overflow-hidden">
					<Image
						layout="fullWidth"
						class="h-full md:w-full"
						width="800"
						height="600"
						src={HOMEPAGE_IMAGE}
						alt="Background header photo of bicycle taken by <PERSON>k<PERSON>"
					/>
				</div>
				<div class="absolute inset-0 bg-gradient-to-br from-background/70 via-background/50 to-background/80" />

				{/* Floating Icons Animation Layer */}
				<FloatingIcons />

				<div class="relative max-w-3xl mx-auto py-32 px-6 flex flex-col items-center text-center sm:py-64 lg:px-0 z-10">
					<div class="relative">
						<h1 class="text-6xl font-extrabold tracking-normal lg:text-6xl text-white drop-shadow-2xl animate-fade-in">
							{$localize`SUGARJAYS`}
						</h1>
						<div class="absolute -inset-1 bg-gradient-to-r from-primary via-accent1 to-secondary rounded-lg blur opacity-20 animate-pulse-slow"></div>
					</div>

					<p class="mt-4 text-2xl text-white">
						{$localize`Custom`}{' '}
						<Link href="#" class="text-accent1 hover:text-accent2 transition-colors">
							Collaborative Apparel
						</Link>{' '}
						<span class="text-primary">Est. 2024</span>
					</p>

					<div
						class="mt-8 flex flex-col sm:flex-row gap-4 animate-fade-in"
						style="animation-delay: 0.5s;"
					>
						<Link
							href="/collections"
							class="px-8 py-3 rounded-md bg-primary text-white font-medium hover:bg-secondary hover:scale-105 transition-all duration-300 transform hover:shadow-lg hover:shadow-primary/50"
						>
							{$localize`Shop Now`}
						</Link>
						<Link
							href="/about"
							class="px-8 py-3 rounded-md bg-transparent border-2 border-accent1 text-white font-medium hover:bg-accent1 hover:text-background hover:scale-105 transition-all duration-300 transform hover:shadow-lg hover:shadow-accent1/50"
						>
							{$localize`Learn More`}
						</Link>
					</div>
				</div>
			</div>

			<section class="pt-12 xl:max-w-7xl xl:mx-auto xl:px-8">
				<div class="mt-4 flow-root">
					<div class="-my-2">
						<div class="box-content py-2 px-2 relative overflow-x-auto xl:overflow-visible">
							<div class="sm:px-6 lg:px-8 xl:px-0 pb-4">
								<h2 class="text-2xl font-light tracking-tight text-gray-900">{$localize`Shop by Category`}</h2>
							</div>
							<div class="grid justify-items-center grid-cols-2 md:grid-cols-3 gap-y-8 gap-x-8 sm:px-6 lg:px-8 xl:relative xl:px-0 xl:space-x-0 xl:gap-x-8">
								{collections.map((collection) =>
									collection.featuredAsset ? (
										<CollectionCard key={collection.id} collection={collection} />
									) : null
								)}
							</div>
						</div>
					</div>
				</div>
			</section>
		</div>
	);
});
